#!/usr/bin/env python3
"""
Test script to verify dataset loading works correctly
"""

import sys
from pathlib import Path
from transformers import CLIPTokenizer
from torch.utils.data import DataLoader

# Import the dataset class from our training script
sys.path.append('.')
from lora_training import MultiCharacterDataset

def test_dataset_loading():
    """Test that the dataset can load images and captions correctly"""
    
    print("Testing dataset loading...")
    
    # Test with a simple tokenizer (we'll use a lightweight one)
    try:
        tokenizer = CLIPTokenizer.from_pretrained("openai/clip-vit-base-patch32")
    except Exception as e:
        print(f"Could not load tokenizer: {e}")
        print("This is expected if you don't have internet or the model isn't cached")
        return False
    
    # Create dataset
    try:
        dataset = MultiCharacterDataset(
            data_root="training",
            tokenizer=tokenizer,
            size=512  # Smaller size for testing
        )
        
        print(f"Dataset created successfully with {len(dataset)} samples")
        
        if len(dataset) == 0:
            print("ERROR: Dataset is empty!")
            return False
        
        # Test loading a single sample
        sample = dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"Image shape: {sample['pixel_values'].shape}")
        print(f"Input IDs shape: {sample['input_ids'].shape}")
        print(f"Attention mask shape: {sample['attention_mask'].shape}")
        
        # Test DataLoader
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False)
        batch = next(iter(dataloader))
        
        print(f"Batch pixel_values shape: {batch['pixel_values'].shape}")
        print(f"Batch input_ids shape: {batch['input_ids'].shape}")
        
        print("✅ Dataset loading test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Dataset loading test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_structure():
    """Test the data directory structure"""
    
    print("\nTesting data directory structure...")
    
    data_root = Path("training")
    if not data_root.exists():
        print("❌ training directory does not exist")
        return False
    
    character_folders = [d for d in data_root.iterdir() if d.is_dir()]
    print(f"Found {len(character_folders)} character folders: {[d.name for d in character_folders]}")
    
    total_images = 0
    total_captions = 0
    
    for char_folder in character_folders:
        print(f"\nChecking {char_folder.name}:")
        
        # Check for images
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]
        images = []
        for ext in image_extensions:
            images.extend(list(char_folder.glob(ext)))
        
        print(f"  Found {len(images)} images")
        total_images += len(images)
        
        # Check for captions
        captions = list(char_folder.glob("*.txt"))
        print(f"  Found {len(captions)} caption files")
        total_captions += len(captions)
        
        # Check matching pairs
        matched_pairs = 0
        for img in images:
            caption_file = img.with_suffix(".txt")
            if caption_file.exists():
                matched_pairs += 1
        
        print(f"  Found {matched_pairs} image-caption pairs")
    
    print(f"\nTotal: {total_images} images, {total_captions} captions")
    
    if total_images == 0:
        print("❌ No images found!")
        return False
    
    if total_captions == 0:
        print("❌ No caption files found!")
        return False
    
    print("✅ Data structure test PASSED!")
    return True

if __name__ == "__main__":
    print("Running dataset tests...\n")
    
    # Test data structure first
    structure_ok = test_data_structure()
    
    if structure_ok:
        # Test dataset loading
        dataset_ok = test_dataset_loading()
        
        if dataset_ok:
            print("\n🎉 All tests passed! Your dataset is ready for training.")
        else:
            print("\n❌ Dataset loading failed. Check the error messages above.")
    else:
        print("\n❌ Data structure issues found. Please fix them before training.")
