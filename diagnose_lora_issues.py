#!/usr/bin/env python3
"""
Comprehensive LoRA Training and Inference Diagnostic Tool
Identifies and provides solutions for character consistency issues
"""

import json
import torch
from pathlib import Path
import argparse
from typing import Dict, List, Optional
import os

class LoRADiagnostic:
    """Comprehensive diagnostic tool for LoRA training issues"""
    
    def __init__(self):
        self.issues = []
        self.recommendations = []
        
    def diagnose_training_data(self, data_root: str):
        """Analyze training data for potential issues"""
        print("🔍 ANALYZING TRAINING DATA")
        print("=" * 50)
        
        data_path = Path(data_root)
        if not data_path.exists():
            self.issues.append(f"Training data directory '{data_root}' not found")
            return
            
        characters = []
        total_images = 0
        
        for char_folder in data_path.iterdir():
            if char_folder.is_dir():
                char_name = char_folder.name
                images = []
                captions = []
                
                # Count images and captions
                for ext in ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]:
                    for img_file in char_folder.glob(ext):
                        caption_file = img_file.with_suffix(".txt")
                        if caption_file.exists():
                            images.append(img_file)
                            try:
                                with open(caption_file, 'r', encoding='utf-8') as f:
                                    caption = f.read().strip()
                                captions.append(caption)
                            except Exception as e:
                                self.issues.append(f"Could not read caption file {caption_file}: {e}")
                
                characters.append({
                    'name': char_name,
                    'images': len(images),
                    'captions': captions
                })
                total_images += len(images)
                
                print(f"📁 Character: {char_name}")
                print(f"   Images: {len(images)}")
                
                # Analyze captions for this character
                if captions:
                    # Check caption format consistency
                    sks_count = sum(1 for cap in captions if 'sks' in cap.lower())
                    char_name_count = sum(1 for cap in captions if char_name.lower() in cap.lower())
                    
                    print(f"   Captions with 'sks': {sks_count}/{len(captions)}")
                    print(f"   Captions with character name: {char_name_count}/{len(captions)}")
                    
                    # Sample captions
                    print(f"   Sample captions:")
                    for i, caption in enumerate(captions[:3]):
                        print(f"     {i+1}. {caption}")
                    
                    # Check for issues
                    if sks_count < len(captions) * 0.8:
                        self.issues.append(f"Character {char_name}: Only {sks_count}/{len(captions)} captions contain 'sks' token")
                        self.recommendations.append(f"Fix captions for {char_name} to include 'sks {char_name}' format")
                    
                    if len(images) < 10:
                        self.issues.append(f"Character {char_name}: Only {len(images)} training images (recommended: 15-30)")
                        self.recommendations.append(f"Add more training images for {char_name}")
                else:
                    self.issues.append(f"Character {char_name}: No valid captions found")
                
                print()
        
        print(f"📊 SUMMARY:")
        print(f"   Total characters: {len(characters)}")
        print(f"   Total images: {total_images}")
        
        if total_images < 20:
            self.issues.append(f"Very few training images ({total_images}). Recommended: 50+ for good results")
            
        return characters
    
    def diagnose_checkpoint(self, checkpoint_path: str):
        """Analyze LoRA checkpoint for issues"""
        print("\n💾 ANALYZING LORA CHECKPOINT")
        print("=" * 50)
        
        checkpoint_dir = Path(checkpoint_path)
        if not checkpoint_dir.exists():
            self.issues.append(f"Checkpoint directory '{checkpoint_path}' not found")
            print(f"❌ Checkpoint directory not found: {checkpoint_path}")
            return None
            
        # Check for weight files
        weight_files = {
            "lora_weights.pt": checkpoint_dir / "lora_weights.pt",
            "pytorch_lora_weights.bin": checkpoint_dir / "pytorch_lora_weights.bin", 
            "pytorch_lora_weights.safetensors": checkpoint_dir / "pytorch_lora_weights.safetensors"
        }
        
        available_files = []
        for name, path in weight_files.items():
            if path.exists():
                available_files.append(name)
                print(f"✅ Found: {name}")
            else:
                print(f"❌ Missing: {name}")
        
        if not available_files:
            self.issues.append("No LoRA weight files found in checkpoint")
            return None
            
        # Load and analyze weights
        weight_file = None
        weights = None
        
        for name in ["pytorch_lora_weights.safetensors", "pytorch_lora_weights.bin", "lora_weights.pt"]:
            if name in available_files:
                weight_file = checkpoint_dir / name
                break
                
        if weight_file:
            try:
                if weight_file.suffix == ".safetensors":
                    from safetensors.torch import load_file
                    weights = load_file(weight_file, device="cpu")
                else:
                    weights = torch.load(weight_file, map_location="cpu")
                    
                print(f"📊 Loaded weights from {weight_file.name}")
                print(f"   Total weight tensors: {len(weights)}")
                
                # Analyze weight structure
                lora_A_count = sum(1 for k in weights.keys() if k.endswith('.lora_A'))
                lora_B_count = sum(1 for k in weights.keys() if k.endswith('.lora_B'))
                
                print(f"   LoRA A matrices: {lora_A_count}")
                print(f"   LoRA B matrices: {lora_B_count}")
                
                if lora_A_count != lora_B_count:
                    self.issues.append(f"Mismatched LoRA matrices: {lora_A_count} A matrices vs {lora_B_count} B matrices")
                
                # Check key prefixes
                transformer_keys = [k for k in weights.keys() if 'transformer' in k]
                text_encoder_keys = [k for k in weights.keys() if 'text_encoder' in k]
                
                print(f"   Transformer keys: {len(transformer_keys)}")
                print(f"   Text encoder keys: {len(text_encoder_keys)}")
                
                # Sample keys
                print(f"   Sample keys:")
                for i, key in enumerate(list(weights.keys())[:5]):
                    print(f"     {i+1}. {key}")
                
                # Check for proper prefixes
                proper_transformer_keys = [k for k in weights.keys() if k.startswith('transformer.')]
                if len(proper_transformer_keys) == 0 and len(transformer_keys) > 0:
                    self.issues.append("LoRA keys don't have proper 'transformer.' prefix")
                    self.recommendations.append("Re-save checkpoint with proper key prefixes")
                
            except Exception as e:
                self.issues.append(f"Could not load weights from {weight_file}: {e}")
                
        # Check config
        config_file = checkpoint_dir / "config.json"
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    
                print(f"📋 Configuration:")
                print(f"   LoRA Rank: {config.get('lora_rank', 'Not specified')}")
                print(f"   LoRA Alpha: {config.get('lora_alpha', 'Not specified')}")
                print(f"   Learning Rate: {config.get('learning_rate', 'Not specified')}")
                print(f"   Epochs: {config.get('num_epochs', 'Not specified')}")
                
                # Check for suboptimal settings
                rank = config.get('lora_rank', 4)
                alpha = config.get('lora_alpha', 32)
                lr = config.get('learning_rate', 1e-4)
                
                if rank < 8:
                    self.recommendations.append(f"Consider increasing LoRA rank from {rank} to 8-16 for better character learning")
                    
                if alpha / rank < 4:
                    self.recommendations.append(f"Consider increasing LoRA alpha for stronger adaptation (current ratio: {alpha/rank:.1f})")
                    
                if lr > 2e-4:
                    self.recommendations.append(f"Learning rate {lr} might be too high, consider 1e-4 or lower")
                    
            except Exception as e:
                self.issues.append(f"Could not read config: {e}")
        else:
            self.issues.append("No config.json found in checkpoint")
            
        return weights
    
    def test_inference_compatibility(self, checkpoint_path: str, model_id: str = "stabilityai/stable-diffusion-3.5-large"):
        """Test if checkpoint can be loaded for inference"""
        print("\n🧪 TESTING INFERENCE COMPATIBILITY")
        print("=" * 50)
        
        try:
            from diffusers import StableDiffusion3Pipeline
            
            # Try loading the pipeline
            print("🔄 Loading SD3.5 pipeline...")
            pipeline = StableDiffusion3Pipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16,
                device_map="auto" if torch.cuda.is_available() else None
            )
            print("✅ Pipeline loaded successfully")
            
            # Try loading LoRA weights
            checkpoint_dir = Path(checkpoint_path)
            if checkpoint_dir.exists():
                try:
                    print("🔄 Testing LoRA weight loading...")
                    pipeline.load_lora_weights(checkpoint_dir)
                    print("✅ LoRA weights loaded successfully with pipeline method")
                    return True
                except Exception as e:
                    print(f"❌ Pipeline LoRA loading failed: {e}")
                    self.issues.append(f"LoRA weights incompatible with pipeline loading: {e}")
                    
                    # This is where the warnings you're seeing come from
                    if "No LoRA keys associated" in str(e):
                        self.issues.append("LoRA key naming doesn't match expected Diffusers format")
                        self.recommendations.append("Re-save checkpoint with proper Diffusers-compatible key names")
                    
                    return False
            else:
                self.issues.append(f"Checkpoint path {checkpoint_path} not found")
                return False
                
        except Exception as e:
            print(f"❌ Could not test inference: {e}")
            self.issues.append(f"Inference test failed: {e}")
            return False
    
    def generate_fix_script(self, checkpoint_path: str, output_path: str = None):
        """Generate a script to fix the identified issues"""
        if output_path is None:
            output_path = "fix_lora_checkpoint.py"
            
        fix_script = f'''#!/usr/bin/env python3
"""
Auto-generated script to fix LoRA checkpoint issues
Generated by diagnose_lora_issues.py
"""

import torch
from pathlib import Path

def fix_checkpoint(checkpoint_path: str):
    """Fix LoRA checkpoint for proper Diffusers compatibility"""
    checkpoint_dir = Path(checkpoint_path)
    
    # Load existing weights
    weight_files = [
        "lora_weights.pt",
        "pytorch_lora_weights.bin", 
        "pytorch_lora_weights.safetensors"
    ]
    
    weights = None
    for filename in weight_files:
        weight_file = checkpoint_dir / filename
        if weight_file.exists():
            if filename.endswith('.safetensors'):
                from safetensors.torch import load_file
                weights = load_file(weight_file, device="cpu")
            else:
                weights = torch.load(weight_file, map_location="cpu")
            break
    
    if weights is None:
        print("❌ No weight files found")
        return False
    
    # Fix key naming
    fixed_weights = {{}}
    for key, value in weights.items():
        # Ensure proper transformer prefix
        if not key.startswith('transformer.') and 'transformer' not in key:
            if key.startswith('transformer_blocks.'):
                new_key = f"transformer.{{key}}"
            else:
                new_key = key
        else:
            new_key = key
            
        fixed_weights[new_key] = value
    
    # Save fixed weights
    torch.save(fixed_weights, checkpoint_dir / "pytorch_lora_weights_fixed.bin")
    
    try:
        from safetensors.torch import save_file
        save_file(fixed_weights, checkpoint_dir / "pytorch_lora_weights_fixed.safetensors")
    except ImportError:
        pass
    
    print(f"✅ Fixed checkpoint saved with {{len(fixed_weights)}} weight tensors")
    return True

if __name__ == "__main__":
    fix_checkpoint("{checkpoint_path}")
'''
        
        with open(output_path, 'w') as f:
            f.write(fix_script)
            
        print(f"📝 Generated fix script: {output_path}")
        return output_path
    
    def print_summary(self):
        """Print diagnostic summary and recommendations"""
        print("\n" + "=" * 60)
        print("🎯 DIAGNOSTIC SUMMARY")
        print("=" * 60)
        
        if self.issues:
            print(f"\n❌ ISSUES FOUND ({len(self.issues)}):")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        else:
            print("\n✅ No critical issues found!")
            
        if self.recommendations:
            print(f"\n💡 RECOMMENDATIONS ({len(self.recommendations)}):")
            for i, rec in enumerate(self.recommendations, 1):
                print(f"   {i}. {rec}")
                
        print("\n" + "=" * 60)

def main():
    parser = argparse.ArgumentParser(description="Diagnose LoRA training and inference issues")
    parser.add_argument("--data_root", type=str, help="Training data directory")
    parser.add_argument("--checkpoint_path", type=str, help="LoRA checkpoint directory")
    parser.add_argument("--model_id", type=str, default="stabilityai/stable-diffusion-3.5-large")
    parser.add_argument("--generate_fix", action="store_true", help="Generate fix script")
    
    args = parser.parse_args()
    
    diagnostic = LoRADiagnostic()
    
    if args.data_root:
        diagnostic.diagnose_training_data(args.data_root)
        
    if args.checkpoint_path:
        diagnostic.diagnose_checkpoint(args.checkpoint_path)
        diagnostic.test_inference_compatibility(args.checkpoint_path, args.model_id)
        
        if args.generate_fix:
            diagnostic.generate_fix_script(args.checkpoint_path)
    
    diagnostic.print_summary()

if __name__ == "__main__":
    main()
