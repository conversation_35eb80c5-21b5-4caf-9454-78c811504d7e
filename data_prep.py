#!/usr/bin/env python3
"""
Data Preparation Helper for Multi-Character LoRA Training
Helps organize and validate your training data with AI-powered caption generation
"""

import os
import json
from pathlib import Path
from PIL import Image
import argparse
from typing import Dict, List, Tuple, Optional
import shutil
import torch
from transformers import <PERSON>lipProcessor, BlipForConditionalGeneration
import warnings
warnings.filterwarnings("ignore")

class DataPreparator:
    """Helper class for preparing training data"""

    def __init__(self, data_root: str):
        self.data_root = Path(data_root)
        self.data_root.mkdir(parents=True, exist_ok=True)
        self.blip_processor = None
        self.blip_model = None

    def _load_blip_model(self):
        """Load BLIP model for image captioning"""
        if self.blip_processor is None or self.blip_model is None:
            print("🤖 Loading BLIP model for AI caption generation...")
            print("   📥 This may take a few minutes on first run to download the model...")
            try:
                # Use a smaller, faster model for better compatibility
                model_name = "Salesforce/blip-image-captioning-base"
                print(f"   📦 Downloading {model_name}...")

                self.blip_processor = BlipProcessor.from_pretrained(model_name)
                self.blip_model = BlipForConditionalGeneration.from_pretrained(model_name)

                # Move to GPU if available
                if torch.cuda.is_available():
                    self.blip_model = self.blip_model.cuda()
                    print("   ✅ Model loaded on GPU")
                else:
                    print("   ✅ Model loaded on CPU")

            except Exception as e:
                print(f"   ❌ Error loading BLIP model: {e}")
                print("   💡 Possible solutions:")
                print("      - Check your internet connection")
                print("      - Try again later (HuggingFace servers might be busy)")
                print("      - Make sure you have: pip install transformers torch")
                raise
        
    def validate_data(self) -> Dict:
        """Validate the data structure and return statistics"""
        stats = {
            "characters": {},
            "total_images": 0,
            "total_characters": 0,
            "issues": []
        }
        
        print("🔍 Validating data structure...")
        
        for char_folder in self.data_root.iterdir():
            if not char_folder.is_dir():
                continue
                
            char_name = char_folder.name
            char_stats = {
                "images": 0,
                "captions": 0,
                "matched_pairs": 0,
                "issues": []
            }
            
            # Count images and captions
            images = list(char_folder.glob("*.jpg")) + list(char_folder.glob("*.png"))
            captions = list(char_folder.glob("*.txt"))
            
            char_stats["images"] = len(images)
            char_stats["captions"] = len(captions)
            
            # Check for matching pairs
            for img_file in images:
                caption_file = img_file.with_suffix(".txt")
                if caption_file.exists():
                    char_stats["matched_pairs"] += 1
                    
                    # Validate image
                    try:
                        with Image.open(img_file) as img:
                            if img.size[0] < 512 or img.size[1] < 512:
                                char_stats["issues"].append(f"Low resolution: {img_file.name}")
                    except Exception as e:
                        char_stats["issues"].append(f"Corrupted image: {img_file.name}")
                    
                    # Validate caption
                    try:
                        with open(caption_file, 'r', encoding='utf-8') as f:
                            caption = f.read().strip()
                            if len(caption) < 10:
                                char_stats["issues"].append(f"Short caption: {caption_file.name}")
                    except Exception as e:
                        char_stats["issues"].append(f"Caption read error: {caption_file.name}")
                else:
                    char_stats["issues"].append(f"Missing caption: {img_file.name}")
            
            stats["characters"][char_name] = char_stats
            stats["total_images"] += char_stats["matched_pairs"]
            
        stats["total_characters"] = len(stats["characters"])
        
        return stats
    
    def print_validation_report(self, stats: Dict):
        """Print a detailed validation report"""
        print("\n📊 VALIDATION REPORT")
        print("=" * 50)
        print(f"Total Characters: {stats['total_characters']}")
        print(f"Total Valid Image-Caption Pairs: {stats['total_images']}")
        print()
        
        for char_name, char_stats in stats["characters"].items():
            print(f"📁 {char_name.upper()}")
            print(f"   Images: {char_stats['images']}")
            print(f"   Captions: {char_stats['captions']}")
            print(f"   Valid Pairs: {char_stats['matched_pairs']}")
            
            if char_stats["issues"]:
                print(f"   ⚠️  Issues ({len(char_stats['issues'])}):")
                for issue in char_stats["issues"][:5]:  # Show first 5 issues
                    print(f"      - {issue}")
                if len(char_stats["issues"]) > 5:
                    print(f"      ... and {len(char_stats['issues']) - 5} more")
            else:
                print(f"   ✅ No issues found")
            print()
        
        # Recommendations
        print("💡 RECOMMENDATIONS")
        print("-" * 30)
        
        min_images_per_char = 10
        low_data_chars = [name for name, stats in stats["characters"].items() 
                         if stats["matched_pairs"] < min_images_per_char]
        
        if low_data_chars:
            print(f"⚠️  Characters with less than {min_images_per_char} images:")
            for char in low_data_chars:
                print(f"   - {char}: {stats['characters'][char]['matched_pairs']} images")
            print(f"   Consider adding more training images for better results.")
        else:
            print("✅ All characters have sufficient training data")
        
        if stats["total_images"] < 50:
            print(f"⚠️  Consider adding more training data overall (current: {stats['total_images']})")
        
        print()
    
    def generate_ai_caption(self, image_path: Path, character_name: Optional[str] = None) -> str:
        """Generate AI caption for a single image using BLIP model"""
        try:
            self._load_blip_model()

            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            inputs = self.blip_processor(image, return_tensors="pt")

            # Move to GPU if available
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}

            # Generate caption
            with torch.no_grad():
                out = self.blip_model.generate(**inputs, max_length=50, num_beams=5)

            # Decode caption
            caption = self.blip_processor.decode(out[0], skip_special_tokens=True)

            # Add character name if provided
            if character_name:
                # Clean character name (remove underscores, numbers)
                clean_name = character_name.replace('_', ' ').split()[0] if '_' in character_name else character_name
                caption = f"a photo of {clean_name}, {caption}"

            return caption

        except Exception as e:
            print(f"   ⚠️  Error generating AI caption for {image_path.name}: {e}")
            print("   🔄 Falling back to enhanced template caption...")
            return self._generate_enhanced_template_caption(image_path, character_name)

    def _generate_enhanced_template_caption(self, image_path: Path, character_name: Optional[str] = None) -> str:
        """Generate enhanced template-based caption as fallback"""
        try:
            # Analyze image properties for better captions
            image = Image.open(image_path).convert('RGB')
            width, height = image.size
            aspect_ratio = width / height

            # Determine image type based on aspect ratio and size
            if aspect_ratio > 1.2:
                orientation = "landscape"
            elif aspect_ratio < 0.8:
                orientation = "portrait"
            else:
                orientation = "square"

            # Generate more descriptive captions
            base_templates = [
                "high quality photograph",
                "detailed portrait",
                "professional photo",
                "clear image",
                "well-lit photograph"
            ]

            style_modifiers = [
                "with good lighting",
                "with clear details",
                "in high resolution",
                "with sharp focus",
                "professionally taken"
            ]

            import random
            base = random.choice(base_templates)
            modifier = random.choice(style_modifiers)

            if character_name:
                clean_name = character_name.replace('_', ' ').split()[0] if '_' in character_name else character_name
                # Use the same "sks" token format as training
                caption = f"a {base} of sks {clean_name}, {modifier}, {orientation} format"
            else:
                caption = f"a {base}, {modifier}, {orientation} format"

            return caption

        except Exception as e:
            print(f"   ⚠️  Error analyzing image {image_path.name}: {e}")
            # Ultimate fallback
            if character_name:
                clean_name = character_name.replace('_', ' ').split()[0] if '_' in character_name else character_name
                # Use the same "sks" token format as training
                return f"a photo of sks {clean_name}"
            else:
                return "a photo"

    def auto_caption_images_ai(self, character_name: Optional[str] = None, overwrite: bool = False):
        """Generate AI-powered captions for images that don't have them"""
        if character_name:
            # Process specific character
            char_folders = [self.data_root / character_name]
            if not char_folders[0].exists():
                print(f"❌ Character folder '{character_name}' not found")
                return
        else:
            # Process all character folders
            char_folders = [f for f in self.data_root.iterdir() if f.is_dir()]

        total_processed = 0

        for char_folder in char_folders:
            char_name = char_folder.name
            print(f"\n🤖 Processing character: {char_name}")

            # Find images that need captions
            images_to_process = []
            image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.webp"]

            for ext in image_extensions:
                for img_file in char_folder.glob(ext):
                    caption_file = img_file.with_suffix(".txt")
                    if not caption_file.exists() or overwrite:
                        images_to_process.append(img_file)

            if not images_to_process:
                print(f"   ✅ All images already have captions")
                continue

            print(f"   🏷️  Generating AI captions for {len(images_to_process)} images...")

            for i, img_file in enumerate(images_to_process):
                print(f"   Processing {i+1}/{len(images_to_process)}: {img_file.name}")

                # Generate AI caption
                caption = self.generate_ai_caption(img_file, char_name)

                # Save caption
                caption_file = img_file.with_suffix(".txt")
                with open(caption_file, 'w', encoding='utf-8') as f:
                    f.write(caption)

                print(f"      ✅ Generated: {caption}")
                total_processed += 1

        print(f"\n🎉 Successfully generated {total_processed} AI captions!")

    def auto_caption_images_smart(self, character_name: Optional[str] = None, overwrite: bool = False):
        """Generate enhanced template captions without AI model download"""
        if character_name:
            # Process specific character
            char_folders = [self.data_root / character_name]
            if not char_folders[0].exists():
                print(f"❌ Character folder '{character_name}' not found")
                return
        else:
            # Process all character folders
            char_folders = [f for f in self.data_root.iterdir() if f.is_dir()]

        total_processed = 0

        for char_folder in char_folders:
            char_name = char_folder.name
            print(f"\n🎨 Processing character: {char_name}")

            # Find images that need captions
            images_to_process = []
            image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.webp"]

            for ext in image_extensions:
                for img_file in char_folder.glob(ext):
                    caption_file = img_file.with_suffix(".txt")
                    if not caption_file.exists() or overwrite:
                        images_to_process.append(img_file)

            if not images_to_process:
                print(f"   ✅ All images already have captions")
                continue

            print(f"   🏷️  Generating smart captions for {len(images_to_process)} images...")

            for i, img_file in enumerate(images_to_process):
                print(f"   Processing {i+1}/{len(images_to_process)}: {img_file.name}")

                # Generate enhanced template caption
                caption = self._generate_enhanced_template_caption(img_file, char_name)

                # Save caption
                caption_file = img_file.with_suffix(".txt")
                with open(caption_file, 'w', encoding='utf-8') as f:
                    f.write(caption)

                print(f"      ✅ Generated: {caption}")
                total_processed += 1

        print(f"\n🎉 Successfully generated {total_processed} smart captions!")

    def auto_caption_images(self, character_name: str, base_description: str):
        """Generate basic template captions for images that don't have them"""
        char_folder = self.data_root / character_name

        if not char_folder.exists():
            print(f"❌ Character folder '{character_name}' not found")
            return

        images_without_captions = []
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.webp"]

        for ext in image_extensions:
            for img_file in char_folder.glob(ext):
                caption_file = img_file.with_suffix(".txt")
                if not caption_file.exists():
                    images_without_captions.append(img_file)

        print(f"🏷️  Generating template captions for {len(images_without_captions)} images...")

        caption_templates = [
            f"{base_description}, portrait",
            f"{base_description}, full body",
            f"{base_description}, upper body",
            f"{base_description}, detailed face",
            f"{base_description}, high quality photo"
        ]

        for i, img_file in enumerate(images_without_captions):
            caption = caption_templates[i % len(caption_templates)]
            caption_file = img_file.with_suffix(".txt")

            with open(caption_file, 'w', encoding='utf-8') as f:
                f.write(caption)

            print(f"   ✅ {img_file.name} -> {caption}")
    
    def resize_images(self, target_size: int = 1024):
        """Resize all images to target size while maintaining aspect ratio"""
        print(f"🖼️  Resizing images to {target_size}x{target_size}...")
        
        for char_folder in self.data_root.iterdir():
            if not char_folder.is_dir():
                continue
            
            images = list(char_folder.glob("*.jpg")) + list(char_folder.glob("*.png"))
            
            for img_file in images:
                try:
                    with Image.open(img_file) as img:
                        # Skip if already correct size
                        if img.size == (target_size, target_size):
                            continue
                        
                        # Resize maintaining aspect ratio
                        img.thumbnail((target_size, target_size), Image.LANCZOS)
                        
                        # Create square image with padding if needed
                        square_img = Image.new('RGB', (target_size, target_size), (255, 255, 255))
                        offset = ((target_size - img.size[0]) // 2, (target_size - img.size[1]) // 2)
                        square_img.paste(img, offset)
                        
                        # Save
                        square_img.save(img_file, quality=95)
                        print(f"   ✅ Resized {img_file.name}")
                        
                except Exception as e:
                    print(f"   ❌ Error resizing {img_file.name}: {e}")
    
    def create_sample_structure(self):
        """Create a sample data structure with example files"""
        print("📁 Creating sample data structure...")
        
        sample_chars = ["character1", "character2"]
        
        for char_name in sample_chars:
            char_folder = self.data_root / char_name
            char_folder.mkdir(exist_ok=True)
            
            # Create example files
            for i in range(3):
                # Create placeholder caption files
                txt_name = f"image_{i+1:02d}.txt"
                
                with open(char_folder / txt_name, 'w') as f:
                    captions = [
                        f"a portrait of {char_name}, high quality, detailed",
                        f"a full body shot of {char_name}, standing pose",
                        f"a close-up of {char_name}, dramatic lighting"
                    ]
                    f.write(captions[i])
                
                print(f"   📄 Created {char_name}/{txt_name}")
        
        print("\n💡 Sample structure created! Replace placeholder files with your actual images.")
        print("   Each image should have a corresponding .txt file with the same name.")
    
    def export_config(self, output_file: str = "training_config.json"):
        """Export a recommended training configuration"""
        stats = self.validate_data()
        
        config = {
            "data_root": str(self.data_root),
            "output_dir": "./outputs",
            "model_id": "stabilityai/stable-diffusion-3.5-large",
            "resolution": 1024,
            "batch_size": 1,
            "num_epochs": max(10, 100 // max(1, stats["total_images"])),  # Adjust epochs based on data
            "learning_rate": 1e-4,
            "lora_rank": 4,
            "lora_alpha": 32,
            "gradient_accumulation_steps": 4,
            "save_steps": 250,
            "seed": 42
        }
        
        with open(output_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"📋 Training configuration exported to {output_file}")
        return config

def main():
    parser = argparse.ArgumentParser(description="Prepare data for multi-character LoRA training with AI caption generation")
    parser.add_argument("--data_root", type=str, required=True, help="Root directory for character data")
    parser.add_argument("--validate", action="store_true", help="Validate existing data structure")
    parser.add_argument("--create_sample", action="store_true", help="Create sample data structure")
    parser.add_argument("--resize", type=int, help="Resize all images to specified size")
    parser.add_argument("--auto_caption", type=str, nargs=2, metavar=("CHARACTER", "DESCRIPTION"),
                        help="Auto-generate template captions for character")
    parser.add_argument("--ai_caption", type=str, nargs='?', const='all', metavar="CHARACTER",
                        help="Generate AI captions using BLIP model. Use 'all' for all characters or specify character name")
    parser.add_argument("--smart_caption", type=str, nargs='?', const='all', metavar="CHARACTER",
                        help="Generate enhanced template captions (no AI model download). Use 'all' for all characters")
    parser.add_argument("--overwrite_captions", action="store_true",
                        help="Overwrite existing captions when using AI captioning")
    parser.add_argument("--export_config", action="store_true", help="Export training configuration")

    args = parser.parse_args()

    preparator = DataPreparator(args.data_root)

    if args.create_sample:
        preparator.create_sample_structure()

    if args.validate:
        stats = preparator.validate_data()
        preparator.print_validation_report(stats)

    if args.resize:
        preparator.resize_images(args.resize)

    if args.auto_caption:
        character, description = args.auto_caption
        preparator.auto_caption_images(character, description)

    if args.ai_caption:
        character_name = None if args.ai_caption == 'all' else args.ai_caption
        preparator.auto_caption_images_ai(character_name, overwrite=args.overwrite_captions)

    if args.smart_caption:
        character_name = None if args.smart_caption == 'all' else args.smart_caption
        preparator.auto_caption_images_smart(character_name, overwrite=args.overwrite_captions)

    if args.export_config:
        preparator.export_config()

    if not any([args.validate, args.create_sample, args.resize, args.auto_caption, args.ai_caption, args.smart_caption, args.export_config]):
        print("No action specified. Use --help to see available options.")
        print("\n💡 NEW CAPTION OPTIONS:")
        print("   --ai_caption all      : Generate AI-powered captions (requires model download)")
        print("   --smart_caption all   : Generate enhanced template captions (no download needed)")
        print("   Example: python data_prep.py --data_root ./training_data --smart_caption all")

if __name__ == "__main__":
    main()