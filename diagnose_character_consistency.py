#!/usr/bin/env python3
"""
Comprehensive diagnosis script for character consistency issues in LoRA training
This script analyzes training data, model configuration, and provides recommendations
"""

import json
import torch
from pathlib import Path
import argparse
from collections import defaultdict
import re

def analyze_training_data(data_root: str):
    """Analyze the training data for potential issues"""
    print("🔍 ANALYZING TRAINING DATA")
    print("=" * 50)
    
    data_path = Path(data_root)
    if not data_path.exists():
        print(f"❌ Data directory '{data_root}' not found")
        return {}
    
    analysis = {
        'characters': {},
        'total_images': 0,
        'caption_issues': [],
        'recommendations': []
    }
    
    for char_folder in data_path.iterdir():
        if not char_folder.is_dir():
            continue
            
        char_name = char_folder.name
        char_data = {
            'images': 0,
            'captions': 0,
            'caption_formats': defaultdict(int),
            'missing_captions': [],
            'caption_samples': []
        }
        
        # Count images and captions
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.webp", "*.bmp", "*.tiff"]
        for ext in image_extensions:
            for img_file in char_folder.glob(ext):
                char_data['images'] += 1
                analysis['total_images'] += 1
                
                caption_file = img_file.with_suffix(".txt")
                if caption_file.exists():
                    char_data['captions'] += 1
                    
                    # Analyze caption format
                    try:
                        with open(caption_file, 'r', encoding='utf-8') as f:
                            caption = f.read().strip()
                            char_data['caption_samples'].append(caption)
                            
                            # Check format patterns
                            if f"sks {char_name}" in caption:
                                char_data['caption_formats']['sks_format'] += 1
                            elif f"a photo of {char_name}" in caption:
                                char_data['caption_formats']['basic_format'] += 1
                            elif char_name in caption:
                                char_data['caption_formats']['contains_name'] += 1
                            else:
                                char_data['caption_formats']['no_name'] += 1
                                
                    except Exception as e:
                        analysis['caption_issues'].append(f"Error reading {caption_file}: {e}")
                else:
                    char_data['missing_captions'].append(img_file.name)
        
        analysis['characters'][char_name] = char_data
        
        # Print character analysis
        print(f"\n📁 Character: {char_name}")
        print(f"   Images: {char_data['images']}")
        print(f"   Captions: {char_data['captions']}")
        print(f"   Missing captions: {len(char_data['missing_captions'])}")
        
        if char_data['caption_formats']:
            print(f"   Caption formats:")
            for fmt, count in char_data['caption_formats'].items():
                print(f"     - {fmt}: {count}")
        
        if char_data['caption_samples']:
            print(f"   Sample captions:")
            for i, sample in enumerate(char_data['caption_samples'][:3]):
                print(f"     {i+1}. {sample}")
        
        if char_data['missing_captions']:
            print(f"   Missing captions: {char_data['missing_captions'][:5]}")
            if len(char_data['missing_captions']) > 5:
                print(f"     ... and {len(char_data['missing_captions']) - 5} more")
    
    # Generate recommendations
    print(f"\n📊 OVERALL ANALYSIS")
    print("=" * 50)
    print(f"Total characters: {len(analysis['characters'])}")
    print(f"Total images: {analysis['total_images']}")
    
    # Check for issues and generate recommendations
    for char_name, char_data in analysis['characters'].items():
        if char_data['images'] < 10:
            analysis['recommendations'].append(
                f"⚠️  {char_name}: Only {char_data['images']} images. Recommend at least 15-20 for good results."
            )
        
        if char_data['captions'] < char_data['images']:
            missing = char_data['images'] - char_data['captions']
            analysis['recommendations'].append(
                f"❌ {char_name}: {missing} images missing captions. Use data_prep.py to generate them."
            )
        
        if char_data['caption_formats']['sks_format'] == 0 and char_data['captions'] > 0:
            analysis['recommendations'].append(
                f"🔧 {char_name}: No captions use 'sks' format. Use fix_captions.py to update them."
            )
        
        if char_data['caption_formats']['no_name'] > 0:
            analysis['recommendations'].append(
                f"⚠️  {char_name}: {char_data['caption_formats']['no_name']} captions don't mention character name."
            )
    
    return analysis

def analyze_model_config(config_path: str = None):
    """Analyze model configuration for potential issues"""
    print(f"\n🔧 ANALYZING MODEL CONFIGURATION")
    print("=" * 50)
    
    config_issues = []
    recommendations = []
    
    if config_path and Path(config_path).exists():
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            print(f"Configuration loaded from: {config_path}")
            
            # Check key parameters
            lora_rank = config.get('lora_rank', 4)
            lora_alpha = config.get('lora_alpha', 32)
            learning_rate = config.get('learning_rate', 1e-4)
            batch_size = config.get('batch_size', 1)
            num_epochs = config.get('num_epochs', 10)
            
            print(f"LoRA Rank: {lora_rank}")
            print(f"LoRA Alpha: {lora_alpha}")
            print(f"Learning Rate: {learning_rate}")
            print(f"Batch Size: {batch_size}")
            print(f"Epochs: {num_epochs}")
            
            # Check for potential issues
            if lora_rank < 4:
                recommendations.append("Consider increasing LoRA rank to 8-16 for better character learning")
            
            if lora_alpha / lora_rank < 4:
                recommendations.append("Consider increasing LoRA alpha for stronger adaptation")
            
            if learning_rate > 5e-4:
                recommendations.append("Learning rate might be too high, consider 1e-4 to 5e-4")
            
            if batch_size > 2:
                recommendations.append("Large batch size might hurt character consistency, try batch_size=1")
                
        except Exception as e:
            config_issues.append(f"Error reading config: {e}")
    else:
        print("No configuration file provided or found")
        recommendations.append("Create a proper training configuration with data_prep.py --export_config")
    
    return config_issues, recommendations

def check_lora_weights(checkpoint_path: str):
    """Analyze LoRA checkpoint for potential issues"""
    print(f"\n💾 ANALYZING LORA CHECKPOINT")
    print("=" * 50)
    
    checkpoint_dir = Path(checkpoint_path)
    if not checkpoint_dir.exists():
        print(f"❌ Checkpoint directory '{checkpoint_path}' not found")
        return []
    
    lora_weights_file = checkpoint_dir / "lora_weights.pt"
    if not lora_weights_file.exists():
        print(f"❌ LoRA weights file not found in {checkpoint_path}")
        return ["LoRA weights file missing"]
    
    try:
        weights = torch.load(lora_weights_file, map_location='cpu')
        
        print(f"✅ LoRA weights loaded successfully")
        print(f"Number of LoRA layers: {len(weights) // 2}")  # Each layer has A and B matrices
        
        # Analyze weight statistics
        total_params = 0
        layer_types = defaultdict(int)
        
        for key, weight in weights.items():
            total_params += weight.numel()
            
            # Categorize layer types
            if 'attn' in key:
                layer_types['attention'] += 1
            elif 'mlp' in key:
                layer_types['mlp'] += 1
            elif 'proj' in key:
                layer_types['projection'] += 1
            else:
                layer_types['other'] += 1
        
        print(f"Total LoRA parameters: {total_params:,}")
        print(f"Layer types:")
        for layer_type, count in layer_types.items():
            print(f"  - {layer_type}: {count}")
        
        recommendations = []
        if total_params < 100000:
            recommendations.append("LoRA has very few parameters, consider increasing rank or adding more layers")
        
        if layer_types['attention'] == 0:
            recommendations.append("No attention layers found in LoRA - this might hurt character consistency")
        
        return recommendations
        
    except Exception as e:
        return [f"Error loading LoRA weights: {e}"]

def main():
    parser = argparse.ArgumentParser(description="Diagnose character consistency issues")
    parser.add_argument("--data_root", type=str, required=True, help="Training data directory")
    parser.add_argument("--config", type=str, help="Training configuration file")
    parser.add_argument("--checkpoint", type=str, help="LoRA checkpoint directory")
    
    args = parser.parse_args()
    
    print("🔍 CHARACTER CONSISTENCY DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Analyze training data
    data_analysis = analyze_training_data(args.data_root)
    
    # Analyze model configuration
    config_issues, config_recommendations = analyze_model_config(args.config)
    
    # Analyze checkpoint if provided
    checkpoint_recommendations = []
    if args.checkpoint:
        checkpoint_recommendations = check_lora_weights(args.checkpoint)
    
    # Print final recommendations
    print(f"\n🎯 RECOMMENDATIONS")
    print("=" * 50)
    
    all_recommendations = (
        data_analysis.get('recommendations', []) + 
        config_recommendations + 
        checkpoint_recommendations
    )
    
    if all_recommendations:
        for i, rec in enumerate(all_recommendations, 1):
            print(f"{i}. {rec}")
    else:
        print("✅ No major issues found!")
    
    print(f"\n💡 NEXT STEPS:")
    print("1. Fix caption format: python fix_captions.py --data_root ./training")
    print("2. Generate missing captions: python data_prep.py --data_root ./training --smart_caption all")
    print("3. Retrain with improved settings")
    print("4. Test with consistent prompts: python lora_testing.py --test_characters character1")

if __name__ == "__main__":
    main()
