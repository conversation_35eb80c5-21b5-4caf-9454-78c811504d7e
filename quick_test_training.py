#!/usr/bin/env python3
"""
Quick test training script to verify the fixes work
Runs a short training session to create a test checkpoint
"""

import subprocess
import sys
import os
from pathlib import Path

def run_quick_training():
    """Run a quick training session for testing"""
    print("🚀 Starting quick test training...")
    print("This will create a small checkpoint to test the fixes")
    
    # Training parameters for quick test
    cmd = [
        sys.executable, "lora_training.py",
        "--data_root", "./training",
        "--output_dir", "./test_outputs",
        "--lora_rank", "4",  # Small rank for quick training
        "--lora_alpha", "32",
        "--learning_rate", "1e-4",
        "--num_epochs", "2",  # Just 2 epochs for testing
        "--batch_size", "1",
        "--mixed_precision", "fp16",
        "--save_steps", "50"  # Save frequently for testing
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        # Run training
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            print("✅ Quick training completed successfully!")
            print("📁 Check ./test_outputs/ for the checkpoint")
            
            # List created files
            output_dir = Path("./test_outputs")
            if output_dir.exists():
                print("\n📋 Created files:")
                for item in output_dir.rglob("*"):
                    if item.is_file():
                        print(f"  - {item}")
            
            return True
        else:
            print("❌ Training failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Training timed out (30 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error running training: {e}")
        return False

def test_inference(checkpoint_path):
    """Test inference with the created checkpoint"""
    print(f"\n🧪 Testing inference with checkpoint: {checkpoint_path}")
    
    cmd = [
        sys.executable, "lora_inference_fixed.py",
        "--lora_path", checkpoint_path,
        "--prompt", "a photo of sks character1, portrait, high quality",
        "--output_dir", "./quick_test_results",
        "--steps", "10"  # Quick generation
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        
        if result.returncode == 0:
            print("✅ Inference test successful!")
            print("📁 Check ./quick_test_results/ for generated image")
            return True
        else:
            print("❌ Inference test failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running inference: {e}")
        return False

def main():
    print("🔧 LoRA Fix Verification Test")
    print("=" * 50)
    
    # Check if training data exists
    if not Path("./training").exists():
        print("❌ Training data directory './training' not found")
        print("Please ensure your training data is in the './training' directory")
        return
    
    # Check if we have the required files
    required_files = ["lora_training.py", "lora_inference_fixed.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ Required file '{file}' not found")
            return
    
    print("✅ All required files found")
    
    # Run quick training
    if run_quick_training():
        # Find the latest checkpoint
        output_dir = Path("./test_outputs")
        checkpoints = list(output_dir.glob("checkpoint-*"))
        
        if checkpoints:
            # Use the latest checkpoint
            latest_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
            print(f"📁 Using checkpoint: {latest_checkpoint}")
            
            # Test inference
            if test_inference(str(latest_checkpoint)):
                print("\n🎉 SUCCESS! The fixes are working correctly!")
                print("\n📋 Next steps:")
                print("1. Run full training with more epochs:")
                print("   python lora_training.py --data_root ./training --output_dir ./outputs --num_epochs 15")
                print("2. Test with the fixed inference script:")
                print("   python lora_inference_fixed.py --lora_path ./outputs/checkpoint-final --character character1")
            else:
                print("\n❌ Inference test failed. Check the error messages above.")
        else:
            print("❌ No checkpoints found after training")
    else:
        print("\n❌ Training test failed. Check the error messages above.")
        print("\n🔍 Common issues:")
        print("- GPU memory insufficient (try --mixed_precision no)")
        print("- Missing dependencies (check requirements.txt)")
        print("- CUDA/PyTorch compatibility issues")

if __name__ == "__main__":
    main()
