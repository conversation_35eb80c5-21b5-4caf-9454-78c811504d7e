#!/usr/bin/env python3
"""
Test script to verify dtype consistency in LoRA training
"""

import torch
import torch.nn.functional as F
from lora_training import <PERSON>RALinearLayer, LoRAConv2d

def test_lora_linear_dtype_consistency():
    """Test LoRA Linear layer with different input dtypes"""
    print("Testing LoRA Linear Layer dtype consistency...")
    
    # Create LoRA layer
    lora_layer = LoRALinearLayer(512, 512, rank=4, alpha=32)
    
    # Test with float32 input
    x_fp32 = torch.randn(2, 512, dtype=torch.float32)
    output_fp32 = lora_layer(x_fp32)
    print(f"Input dtype: {x_fp32.dtype}, Output dtype: {output_fp32.dtype}")
    assert output_fp32.dtype == x_fp32.dtype, "Output dtype should match input dtype"
    
    # Test with float16 input
    x_fp16 = torch.randn(2, 512, dtype=torch.float16)
    output_fp16 = lora_layer(x_fp16)
    print(f"Input dtype: {x_fp16.dtype}, Output dtype: {output_fp16.dtype}")
    assert output_fp16.dtype == x_fp16.dtype, "Output dtype should match input dtype"
    
    print("✓ LoRA Linear Layer dtype consistency test passed!")

def test_lora_conv_dtype_consistency():
    """Test LoRA Conv2d layer with different input dtypes"""
    print("\nTesting LoRA Conv2d Layer dtype consistency...")
    
    # Create LoRA conv layer
    lora_layer = LoRAConv2d(64, 64, kernel_size=3, rank=4, alpha=32)
    
    # Test with float32 input
    x_fp32 = torch.randn(2, 64, 32, 32, dtype=torch.float32)
    output_fp32 = lora_layer(x_fp32)
    print(f"Input dtype: {x_fp32.dtype}, Output dtype: {output_fp32.dtype}")
    assert output_fp32.dtype == x_fp32.dtype, "Output dtype should match input dtype"
    
    # Test with float16 input
    x_fp16 = torch.randn(2, 64, 32, 32, dtype=torch.float16)
    output_fp16 = lora_layer(x_fp16)
    print(f"Input dtype: {x_fp16.dtype}, Output dtype: {output_fp16.dtype}")
    assert output_fp16.dtype == x_fp16.dtype, "Output dtype should match input dtype"
    
    print("✓ LoRA Conv2d Layer dtype consistency test passed!")

def test_mixed_precision_operations():
    """Test mixed precision operations that commonly cause issues"""
    print("\nTesting mixed precision operations...")
    
    # Simulate the operations that happen in training
    latents = torch.randn(2, 4, 64, 64, dtype=torch.float16)
    noise = torch.randn_like(latents, dtype=latents.dtype)
    
    # Test noise addition (common source of dtype mismatch)
    timesteps = torch.randint(0, 1000, (2,), device=latents.device).long()
    
    # Create alpha values with correct dtype
    alphas = torch.linspace(0.9999, 0.0001, 1000, dtype=latents.dtype, device=latents.device)
    alphas_cumprod = torch.cumprod(alphas, dim=0)
    
    sqrt_alpha_prod = alphas_cumprod[timesteps] ** 0.5
    sqrt_one_minus_alpha_prod = (1 - alphas_cumprod[timesteps]) ** 0.5
    
    # Reshape for broadcasting
    sqrt_alpha_prod = sqrt_alpha_prod.view(-1, 1, 1, 1)
    sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.view(-1, 1, 1, 1)
    
    # Add noise
    noisy_latents = sqrt_alpha_prod * latents + sqrt_one_minus_alpha_prod * noise
    
    print(f"Latents dtype: {latents.dtype}")
    print(f"Noise dtype: {noise.dtype}")
    print(f"Noisy latents dtype: {noisy_latents.dtype}")
    print(f"Alpha prod dtype: {sqrt_alpha_prod.dtype}")
    
    assert noisy_latents.dtype == latents.dtype, "Noisy latents should have same dtype as input"
    
    print("✓ Mixed precision operations test passed!")

if __name__ == "__main__":
    print("Running dtype consistency tests...\n")
    
    try:
        test_lora_linear_dtype_consistency()
        test_lora_conv_dtype_consistency()
        test_mixed_precision_operations()
        
        print("\n🎉 All tests passed! Your LoRA implementation should handle dtype consistency correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise
