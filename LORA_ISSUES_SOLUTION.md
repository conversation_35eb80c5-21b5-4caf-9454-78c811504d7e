# LoRA Training and Inference Issues - Complete Solution Guide

## 🔍 Root Cause Analysis

Based on your warning messages and code analysis, here are the main issues causing your LoRA model to not generate consistent character images:

### 1. **LoRA Weight Loading Issues** ❌
The warnings you're seeing indicate that LoRA weights aren't being properly applied:
```
No LoRA keys associated to SD3Transformer2DModel found with the prefix='transformer'
No LoRA keys associated to CLIPTextModelWithProjection found with the prefix='text_encoder'
```

**Root Cause**: Your training code saves LoRA weights with incorrect key prefixes that don't match what the Diffusers library expects during inference.

### 2. **Training vs Inference Mismatch** ❌
- **Training**: Only applies LoRA to transformer layers
- **Inference**: Tries to load LoRA for all components (transformer + text encoders)
- **Result**: LoRA weights are effectively ignored during inference

### 3. **Key Naming Convention Problems** ❌
Your training saves keys like: `transformer_blocks.0.attn.to_q.lora_A`
Diffusers expects: `transformer.transformer_blocks.0.attn.to_q.lora_A`

## 🛠️ Complete Solution

### Step 1: Diagnose Current Issues

First, let's analyze your current setup:

```bash
# Diagnose your training data
python diagnose_lora_issues.py --data_root ./training

# If you have a checkpoint, diagnose it too
python diagnose_lora_issues.py --checkpoint_path ./path/to/your/checkpoint --generate_fix
```

### Step 2: Fix Training Data (if needed)

Check your caption format. Each caption should follow this pattern:
```
a photo of sks character1, [description of the image]
```

If your captions need fixing:
```bash
python fix_captions.py --data_root ./training
```

### Step 3: Retrain with Fixed Code

The updated `lora_training.py` now saves weights with proper prefixes. Retrain your model:

```bash
python lora_training.py \
    --data_root ./training \
    --output_dir ./outputs_fixed \
    --lora_rank 8 \
    --lora_alpha 64 \
    --learning_rate 1e-4 \
    --num_epochs 15 \
    --batch_size 1
```

### Step 4: Test with Fixed Inference

Use the new inference script that properly handles LoRA loading:

```bash
# Test character consistency
python lora_inference_fixed.py \
    --lora_path ./outputs_fixed/checkpoint-final \
    --character character1 \
    --output_dir ./test_results_fixed

# Or generate single image
python lora_inference_fixed.py \
    --lora_path ./outputs_fixed/checkpoint-final \
    --prompt "a photo of sks character1, portrait, high quality" \
    --output_dir ./test_results_fixed
```

## 🔧 If You Have Existing Checkpoints

If you already have trained checkpoints that you want to fix without retraining:

### Option 1: Use the Diagnostic Tool
```bash
python diagnose_lora_issues.py --checkpoint_path ./your_checkpoint --generate_fix
# This creates a fix_lora_checkpoint.py script
python fix_lora_checkpoint.py
```

### Option 2: Manual Fix
```bash
python convert_lora_checkpoint.py ./your_checkpoint --output_path ./fixed_checkpoint
```

## 📊 Key Improvements Made

### 1. **Fixed Training Code** (`lora_training.py`)
- ✅ Proper key prefixes (`transformer.` prefix added)
- ✅ Better checkpoint saving format
- ✅ Improved debugging information

### 2. **New Fixed Inference** (`lora_inference_fixed.py`)
- ✅ Multiple LoRA loading methods with fallbacks
- ✅ Proper adapter handling
- ✅ Manual weight application as backup
- ✅ Character consistency testing

### 3. **Diagnostic Tools** (`diagnose_lora_issues.py`)
- ✅ Comprehensive issue detection
- ✅ Training data analysis
- ✅ Checkpoint compatibility testing
- ✅ Automatic fix script generation

## 🎯 Expected Results

After applying these fixes, you should see:

1. **No more warning messages** during LoRA loading
2. **Consistent character appearance** across different prompts
3. **Proper LoRA weight application** (you'll see confirmation messages)
4. **Better character fidelity** in generated images

## 🔍 Verification Steps

### 1. Check LoRA Loading
You should see messages like:
```
✅ Successfully loaded LoRA with scale 0.XXX
```
Instead of the warning messages you were getting.

### 2. Test Character Consistency
Generate multiple images with the same character:
```bash
python lora_inference_fixed.py \
    --lora_path ./outputs_fixed/checkpoint-final \
    --character character1 \
    --num_tests 5
```

### 3. Compare Before/After
- **Before**: Generic faces, no character consistency
- **After**: Consistent character features across different poses/scenes

## 🚨 Common Pitfalls to Avoid

1. **Don't mix old and new checkpoints** - Use either the fixed training code or fix existing checkpoints
2. **Ensure proper caption format** - Always use `sks character_name` in captions
3. **Use sufficient training data** - At least 15-30 images per character
4. **Monitor LoRA loading messages** - No warnings should appear during inference

## 📈 Advanced Optimization

For even better results, consider:

1. **Increase LoRA rank** to 16-32 for complex characters
2. **Adjust LoRA alpha** for stronger/weaker adaptation
3. **Use more training epochs** (20-30) for better convergence
4. **Add regularization** with diverse training images

## 🆘 Troubleshooting

If you still have issues:

1. **Run the diagnostic tool** to identify specific problems
2. **Check the console output** for detailed error messages
3. **Verify file paths** and checkpoint structure
4. **Test with a simple prompt** first before complex scenes

The key insight is that your LoRA weights were being saved but not properly loaded during inference due to naming convention mismatches. The fixes ensure proper communication between training and inference phases.
