#!/usr/bin/env python3
"""
Fix existing training captions to use the proper "sks" token format
This script will update all caption files to use consistent character naming
"""

import os
import re
from pathlib import Path
import argparse

def fix_caption_format(caption_file: Path, character_name: str):
    """Fix a single caption file to use the proper sks format"""
    try:
        with open(caption_file, 'r', encoding='utf-8') as f:
            original_caption = f.read().strip()
        
        # Check if already has sks format
        if f"sks {character_name}" in original_caption:
            return False, original_caption  # Already correct
        
        # Fix the format
        # Pattern: "a photo of character1, ..." -> "a photo of sks character1, ..."
        patterns = [
            (rf"a photo of {character_name}", f"a photo of sks {character_name}"),
            (rf"a portrait of {character_name}", f"a portrait of sks {character_name}"),
            (rf"a {character_name}", f"a sks {character_name}"),
            (rf"{character_name}", f"sks {character_name}")  # Fallback
        ]
        
        fixed_caption = original_caption
        for pattern, replacement in patterns:
            if re.search(pattern, fixed_caption, re.IGNORECASE):
                fixed_caption = re.sub(pattern, replacement, fixed_caption, flags=re.IGNORECASE)
                break
        
        # If no pattern matched, add sks prefix
        if fixed_caption == original_caption and character_name in original_caption:
            fixed_caption = original_caption.replace(character_name, f"sks {character_name}", 1)
        
        # Write the fixed caption
        with open(caption_file, 'w', encoding='utf-8') as f:
            f.write(fixed_caption)
        
        return True, fixed_caption
        
    except Exception as e:
        print(f"Error processing {caption_file}: {e}")
        return False, ""

def fix_all_captions(data_root: str, dry_run: bool = False):
    """Fix all caption files in the data directory"""
    data_path = Path(data_root)
    
    if not data_path.exists():
        print(f"❌ Data directory '{data_root}' not found")
        return
    
    total_files = 0
    fixed_files = 0
    
    print(f"🔍 Scanning directory: {data_root}")
    print(f"{'🔍 DRY RUN MODE - No files will be modified' if dry_run else '✏️  FIXING CAPTIONS'}")
    print()
    
    for char_folder in data_path.iterdir():
        if not char_folder.is_dir():
            continue
            
        character_name = char_folder.name
        print(f"📁 Processing character: {character_name}")
        
        caption_files = list(char_folder.glob("*.txt"))
        if not caption_files:
            print(f"   ⚠️  No caption files found")
            continue
            
        char_fixed = 0
        for caption_file in caption_files:
            total_files += 1
            
            try:
                with open(caption_file, 'r', encoding='utf-8') as f:
                    original_caption = f.read().strip()
                
                # Check if needs fixing
                if f"sks {character_name}" in original_caption:
                    print(f"   ✅ {caption_file.name}: Already correct")
                    continue
                
                if not dry_run:
                    was_fixed, new_caption = fix_caption_format(caption_file, character_name)
                    if was_fixed:
                        print(f"   🔧 {caption_file.name}:")
                        print(f"      Before: {original_caption}")
                        print(f"      After:  {new_caption}")
                        fixed_files += 1
                        char_fixed += 1
                    else:
                        print(f"   ⚠️  {caption_file.name}: No changes needed")
                else:
                    # Dry run - just show what would be changed
                    patterns = [
                        (rf"a photo of {character_name}", f"a photo of sks {character_name}"),
                        (rf"a portrait of {character_name}", f"a portrait of sks {character_name}"),
                        (rf"a {character_name}", f"a sks {character_name}"),
                        (rf"{character_name}", f"sks {character_name}")
                    ]
                    
                    would_fix = original_caption
                    for pattern, replacement in patterns:
                        if re.search(pattern, would_fix, re.IGNORECASE):
                            would_fix = re.sub(pattern, replacement, would_fix, flags=re.IGNORECASE)
                            break
                    
                    if would_fix != original_caption:
                        print(f"   🔧 {caption_file.name} (WOULD FIX):")
                        print(f"      Before: {original_caption}")
                        print(f"      After:  {would_fix}")
                        char_fixed += 1
                    else:
                        print(f"   ✅ {caption_file.name}: No changes needed")
                        
            except Exception as e:
                print(f"   ❌ Error processing {caption_file.name}: {e}")
        
        print(f"   📊 {char_fixed} files {'would be' if dry_run else 'were'} fixed for {character_name}")
        print()
    
    print(f"📊 Summary:")
    print(f"   Total caption files: {total_files}")
    print(f"   Files {'that would be' if dry_run else ''} fixed: {fixed_files}")
    
    if dry_run:
        print(f"\n💡 Run without --dry-run to actually fix the files")

def main():
    parser = argparse.ArgumentParser(description="Fix caption files to use proper sks token format")
    parser.add_argument("--data_root", type=str, required=True, 
                       help="Root directory containing character folders")
    parser.add_argument("--dry-run", action="store_true", 
                       help="Show what would be changed without modifying files")
    
    args = parser.parse_args()
    
    fix_all_captions(args.data_root, dry_run=args.dry_run)

if __name__ == "__main__":
    main()
