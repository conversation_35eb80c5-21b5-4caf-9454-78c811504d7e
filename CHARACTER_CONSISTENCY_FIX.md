# Character Consistency Fix Guide

## Problem Analysis

Based on the code review, I've identified several issues that can cause inconsistent character generation:

### 1. **Caption Format Issues**
- Training captions use generic format: `"a photo of character1, ..."`
- Missing special token format that helps the model learn character identity
- Inconsistent character naming across captions

### 2. **LoRA Configuration Issues**
- LoRA rank too low (4) for complex character learning
- LoRA alpha too low relative to rank
- LoRA only applied to attention layers, missing other important layers

### 3. **Training/Inference Mismatch**
- Training uses one prompt format, inference uses different format
- Different text encoding approaches between training and inference

### 4. **Data Quality Issues**
- Generic character names ("character1") instead of descriptive names
- Insufficient character-specific details in captions

## Solutions Implemented

### 1. **Fixed Caption Format**
- Updated training to use `"a photo of sks character1, ..."` format
- The "sks" token acts as a special identifier for character learning
- Updated inference to match the same format

### 2. **Improved LoRA Configuration**
- Increased LoRA rank from 4 to 8 for better character representation
- Increased LoRA alpha from 32 to 64 for stronger adaptation
- Extended LoRA to more layer types (attention, MLP, projection layers)

### 3. **Enhanced Data Preparation**
- Created tools to fix existing captions
- Improved caption generation with more descriptive templates
- Added diagnostic tools to identify issues

## Step-by-Step Fix Instructions

### Step 1: Diagnose Current Issues
```bash
python diagnose_character_consistency.py --data_root ./training --config ./training_config.json --checkpoint ./outputs/checkpoint-final
```

### Step 2: Fix Existing Captions
```bash
# First, see what would be changed (dry run)
python fix_captions.py --data_root ./training --dry-run

# Then apply the fixes
python fix_captions.py --data_root ./training
```

### Step 3: Generate Better Captions (if needed)
```bash
# Generate enhanced captions for all characters
python data_prep.py --data_root ./training --smart_caption all --overwrite_captions

# Or for specific character
python data_prep.py --data_root ./training --smart_caption character1 --overwrite_captions
```

### Step 4: Retrain with Improved Settings
```bash
python lora_training.py \
    --data_root ./training \
    --output_dir ./outputs_improved \
    --lora_rank 8 \
    --lora_alpha 64 \
    --learning_rate 1e-4 \
    --num_epochs 15 \
    --batch_size 1
```

### Step 5: Test with Consistent Prompts
```bash
python lora_testing.py \
    --lora_path ./outputs_improved/checkpoint-final \
    --test_characters character1 \
    --output_dir ./test_results
```

## Key Changes Made

### Training Script (`lora_training.py`)
1. **Caption Format**: Changed from `"a photo of {char_name}"` to `"a photo of sks {char_name}"`
2. **LoRA Layers**: Extended to include MLP and projection layers
3. **Default Parameters**: Increased rank to 8 and alpha to 64

### Inference Script (`lora_testing.py`)
1. **Prompt Format**: Updated to match training format with "sks" token
2. **LoRA Loading**: Improved error handling and layer application
3. **Debugging**: Added more verbose logging

### Data Preparation (`data_prep.py`)
1. **Caption Templates**: Enhanced with more descriptive and varied captions
2. **Token Format**: All generated captions now use "sks" format

### New Tools
1. **`fix_captions.py`**: Automatically fixes existing caption files
2. **`diagnose_character_consistency.py`**: Comprehensive diagnostic tool

## Expected Improvements

After applying these fixes, you should see:

1. **Better Character Consistency**: The model should generate the same character appearance across different prompts
2. **Stronger Character Identity**: More distinctive character features
3. **Improved Prompt Following**: Better response to character-specific prompts
4. **Reduced Character Drift**: Less variation in character appearance

## Testing Character Consistency

Use these prompts to test character consistency:

```bash
# Test with the improved model
python lora_testing.py \
    --lora_path ./outputs_improved/checkpoint-final \
    --prompt "a photo of sks character1, portrait, high quality" \
    --num_images 5 \
    --seed 42

# Test different scenarios
python lora_testing.py \
    --lora_path ./outputs_improved/checkpoint-final \
    --prompt "a photo of sks character1, full body, standing" \
    --num_images 3

python lora_testing.py \
    --lora_path ./outputs_improved/checkpoint-final \
    --prompt "a photo of sks character1, close-up face, dramatic lighting" \
    --num_images 3
```

## Troubleshooting

### If characters still look inconsistent:
1. Check that all captions use the "sks" format
2. Ensure you have enough training images (15-20 minimum)
3. Try increasing LoRA rank to 16
4. Train for more epochs (20-30)

### If training is slow:
1. Use gradient accumulation instead of larger batch sizes
2. Enable CPU offloading with `--cpu_offload`
3. Use mixed precision with `--mixed_precision fp16`

### If you get memory errors:
1. Reduce batch size to 1
2. Use `--low_vram` flag
3. Enable CPU offloading

## Additional Tips

1. **Character Names**: Use descriptive names instead of "character1" (e.g., "john_smith", "red_haired_woman")
2. **Caption Quality**: Include specific character features in captions
3. **Training Data**: Ensure consistent lighting and quality across training images
4. **Validation**: Always test with the same seed to compare results

## Files Modified/Created

- ✅ `lora_training.py` - Improved LoRA configuration and caption format
- ✅ `lora_testing.py` - Fixed inference prompt format
- ✅ `data_prep.py` - Enhanced caption generation
- ✅ `fix_captions.py` - Tool to fix existing captions
- ✅ `diagnose_character_consistency.py` - Diagnostic tool
- ✅ `CHARACTER_CONSISTENCY_FIX.md` - This guide
