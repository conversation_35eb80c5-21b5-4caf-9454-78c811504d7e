#!/usr/bin/env python3
"""
Convert existing LoRA checkpoint to Diffusers-compatible format
This script converts lora_weights.pt to pytorch_lora_weights.bin/safetensors
"""

import torch
import json
from pathlib import Path
import argparse

def convert_checkpoint(checkpoint_path: str, output_path: str = None):
    """Convert LoRA checkpoint to Diffusers format"""
    checkpoint_dir = Path(checkpoint_path)
    
    if not checkpoint_dir.exists():
        print(f"❌ Checkpoint directory '{checkpoint_path}' not found")
        return False
    
    # Set output path
    if output_path is None:
        output_path = checkpoint_dir
    else:
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"🔄 Converting checkpoint: {checkpoint_dir}")
    print(f"📁 Output directory: {output_path}")
    
    # Load existing weights
    lora_weights_file = checkpoint_dir / "lora_weights.pt"
    if not lora_weights_file.exists():
        print(f"❌ lora_weights.pt not found in {checkpoint_dir}")
        return False
    
    try:
        print("📥 Loading lora_weights.pt...")
        lora_weights = torch.load(lora_weights_file, map_location='cpu')
        print(f"✅ Loaded {len(lora_weights)} weight tensors")
        
        # Save in Diffusers format
        print("💾 Saving as pytorch_lora_weights.bin...")
        torch.save(lora_weights, output_path / "pytorch_lora_weights.bin")
        
        # Try to save as safetensors (preferred format)
        try:
            from safetensors.torch import save_file
            print("💾 Saving as pytorch_lora_weights.safetensors...")
            save_file(lora_weights, output_path / "pytorch_lora_weights.safetensors")
            print("✅ Saved safetensors format")
        except ImportError:
            print("⚠️  safetensors not available, skipping .safetensors save")
        
        # Copy config if it exists
        config_file = checkpoint_dir / "config.json"
        if config_file.exists():
            if output_path != checkpoint_dir:
                import shutil
                shutil.copy2(config_file, output_path / "config.json")
                print("📋 Copied config.json")
        else:
            print("⚠️  No config.json found")
        
        print("✅ Conversion completed successfully!")
        print(f"📁 Files created in {output_path}:")
        for file in output_path.iterdir():
            if file.is_file():
                print(f"  - {file.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Convert LoRA checkpoint to Diffusers format")
    parser.add_argument("checkpoint_path", type=str, help="Path to checkpoint directory")
    parser.add_argument("--output_path", type=str, help="Output directory (default: same as input)")
    
    args = parser.parse_args()
    
    success = convert_checkpoint(args.checkpoint_path, args.output_path)
    
    if success:
        print("\n🎉 Conversion successful!")
        print("You can now use this checkpoint with:")
        print(f"  python lora_testing.py --lora_path {args.output_path or args.checkpoint_path}")
    else:
        print("\n❌ Conversion failed!")
        exit(1)

if __name__ == "__main__":
    main()
