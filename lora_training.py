#!/usr/bin/env python3
"""
Multi-Character LoRA Training Pipeline for Stable Diffusion 3.5
Uses MMDiT (Multimodal Diffusion Transformer) architecture
"""

import os
import json
import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from transformers import CLIPTextModel, CLIPTokenizer, CLIPTextModelWithProjection, T5EncoderModel, T5TokenizerFast
from diffusers import SD3Transformer2DModel, AutoencoderKL, FlowMatchEulerDiscreteScheduler
from diffusers.optimization import get_scheduler
from diffusers.utils import check_min_version
from PIL import Image
import numpy as np
from pathlib import Path
import argparse
import logging
from tqdm import tqdm
import math
from typing import Dict, List, Tuple, Optional
import random
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import set_seed

# Check diffusers version
check_min_version("0.30.0")

# Setup logging
logger = get_logger(__name__)

class LoRALinearLayer(torch.nn.Module):
    """LoRA Linear Layer implementation"""
    def __init__(self, in_features: int, out_features: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        
        # LoRA matrices
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_features) * 0.01)
        self.lora_B = torch.nn.Parameter(torch.zeros(out_features, rank))
        
    def forward(self, x):
        # Ensure consistent dtype
        lora_A = self.lora_A.to(x.dtype)
        lora_B = self.lora_B.to(x.dtype)
        return (x @ lora_A.T @ lora_B.T) * self.scaling

class LoRAConv2d(torch.nn.Module):
    """LoRA Convolutional Layer implementation"""
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int, rank: int = 4, alpha: float = 1.0):
        super().__init__()
        self.rank = rank
        self.alpha = alpha
        self.scaling = alpha / rank
        self.kernel_size = kernel_size
        
        # LoRA matrices for conv layers
        self.lora_A = torch.nn.Parameter(torch.randn(rank, in_channels, kernel_size, kernel_size) * 0.01)
        self.lora_B = torch.nn.Parameter(torch.zeros(out_channels, rank, 1, 1))
        
    def forward(self, x):
        # Ensure consistent dtype
        lora_A = self.lora_A.to(x.dtype)
        lora_B = self.lora_B.to(x.dtype)
        return F.conv2d(x, (lora_B.squeeze() @ lora_A.view(self.rank, -1)).view(
            lora_B.shape[0], lora_A.shape[1], self.kernel_size, self.kernel_size
        ), padding=self.kernel_size//2) * self.scaling

class MultiCharacterDataset(Dataset):
    """Dataset for multi-character training with SD3.5"""

    def __init__(self, data_root: str, tokenizer: CLIPTokenizer, tokenizer_2: CLIPTokenizer, tokenizer_3: T5TokenizerFast, size: int = 1024):
        self.data_root = Path(data_root)
        self.tokenizer = tokenizer
        self.tokenizer_2 = tokenizer_2
        self.tokenizer_3 = tokenizer_3
        self.size = size
        self.images = []
        self.captions = []
        
        # Load data from character folders
        image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff", "*.webp"]

        for char_folder in self.data_root.iterdir():
            if char_folder.is_dir():
                char_name = char_folder.name
                print(f"Processing character folder: {char_name}")

                # Load images and captions for all supported formats
                char_images_count = 0
                for ext in image_extensions:
                    for img_file in char_folder.glob(ext):
                        # Look for corresponding caption file
                        caption_file = img_file.with_suffix(".txt")
                        if caption_file.exists():
                            try:
                                with open(caption_file, 'r', encoding='utf-8') as f:
                                    caption = f.read().strip()

                                # Add character token to caption with more specific formatting
                                # Use a special token format that's more distinctive
                                caption = f"a photo of sks {char_name}, {caption}"

                                self.images.append(str(img_file))
                                self.captions.append(caption)
                                char_images_count += 1
                            except Exception as e:
                                print(f"Warning: Could not read caption file {caption_file}: {e}")
                        else:
                            print(f"Warning: No caption file found for {img_file}")

                print(f"  Found {char_images_count} images with captions for {char_name}")

        print(f"Loaded {len(self.images)} images from {len(list(self.data_root.iterdir()))} characters")

        # Check if dataset is empty
        if len(self.images) == 0:
            raise ValueError(
                f"No images found in {self.data_root}. "
                f"Make sure your data structure is: data_root/character_name/image.ext and data_root/character_name/image.txt "
                f"Supported image formats: {', '.join(image_extensions)}"
            )
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        # Load and preprocess image
        image_path = self.images[idx]
        image = Image.open(image_path).convert("RGB")
        
        # Resize and center crop
        image = image.resize((self.size, self.size), Image.LANCZOS)
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image).permute(2, 0, 1)
        
        # Normalize to [-1, 1]
        image = (image - 0.5) / 0.5
        
        # Tokenize caption for all three text encoders
        caption = self.captions[idx]

        # CLIP tokenizer 1
        text_inputs = self.tokenizer(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        # CLIP tokenizer 2
        text_inputs_2 = self.tokenizer_2(
            caption,
            padding="max_length",
            max_length=77,
            truncation=True,
            return_tensors="pt"
        )

        # T5 tokenizer
        text_inputs_3 = self.tokenizer_3(
            caption,
            padding="max_length",
            max_length=256,  # T5 uses longer sequences
            truncation=True,
            return_tensors="pt"
        )

        return {
            "pixel_values": image,
            "input_ids": text_inputs.input_ids.squeeze(),
            "attention_mask": text_inputs.attention_mask.squeeze(),
            "input_ids_2": text_inputs_2.input_ids.squeeze(),
            "attention_mask_2": text_inputs_2.attention_mask.squeeze(),
            "input_ids_3": text_inputs_3.input_ids.squeeze(),
            "attention_mask_3": text_inputs_3.attention_mask.squeeze()
        }

class LoRATrainer:
    """Multi-Character LoRA Trainer for SD3.5"""

    def __init__(self, config: Dict):
        self.config = config
        self.accelerator = Accelerator(
            gradient_accumulation_steps=config.get("gradient_accumulation_steps", 1),
            mixed_precision=config.get("mixed_precision", "fp16")
        )

        # Set random seed
        if config.get("seed"):
            set_seed(config["seed"])

        self.setup_models()
        self.setup_lora_layers()
        self.verify_dimensions()
        
    def setup_models(self):
        """Initialize SD3.5 models"""
        model_id = self.config.get("model_id", "stabilityai/stable-diffusion-3.5-large")

        # Determine dtype based on mixed precision setting - use bfloat16 for better memory efficiency
        if self.config.get("mixed_precision") == "no":
            model_dtype = torch.float32
        elif self.config.get("mixed_precision") == "bf16":
            model_dtype = torch.bfloat16
        else:
            model_dtype = torch.float16

        # Load tokenizers (SD3.5 has three)
        self.tokenizer = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer"
        )
        self.tokenizer_2 = CLIPTokenizer.from_pretrained(
            model_id, subfolder="tokenizer_2"
        )
        self.tokenizer_3 = T5TokenizerFast.from_pretrained(
            model_id, subfolder="tokenizer_3"
        )

        # Load text encoders (SD3.5 has three) with memory optimization
        self.text_encoder = CLIPTextModel.from_pretrained(
            model_id, subfolder="text_encoder", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )
        self.text_encoder_2 = CLIPTextModelWithProjection.from_pretrained(
            model_id, subfolder="text_encoder_2", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )
        self.text_encoder_3 = T5EncoderModel.from_pretrained(
            model_id, subfolder="text_encoder_3", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load Transformer (MMDiT) instead of UNet with memory optimization
        self.transformer = SD3Transformer2DModel.from_pretrained(
            model_id, subfolder="transformer", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load VAE with memory optimization
        self.vae = AutoencoderKL.from_pretrained(
            model_id, subfolder="vae", torch_dtype=model_dtype, low_cpu_mem_usage=True
        )

        # Load scheduler
        self.scheduler = FlowMatchEulerDiscreteScheduler.from_pretrained(
            model_id, subfolder="scheduler"
        )

        # Freeze original parameters and enable memory efficient attention
        self.text_encoder.requires_grad_(False)
        self.text_encoder_2.requires_grad_(False)
        self.text_encoder_3.requires_grad_(False)
        self.transformer.requires_grad_(False)
        self.vae.requires_grad_(False)

        # Enable memory efficient attention if available
        if hasattr(self.transformer, 'enable_xformers_memory_efficient_attention'):
            try:
                self.transformer.enable_xformers_memory_efficient_attention()
                logger.info("Enabled xformers memory efficient attention")
            except Exception as e:
                logger.warning(f"Could not enable xformers: {e}")

        # Enable gradient checkpointing for memory savings
        if hasattr(self.transformer, 'enable_gradient_checkpointing'):
            self.transformer.enable_gradient_checkpointing()
            logger.info("Enabled gradient checkpointing")
        
    def setup_lora_layers(self):
        """Setup LoRA layers for SD3.5 Transformer"""
        self.lora_layers = {}
        rank = self.config.get("lora_rank", 4)
        alpha = self.config.get("lora_alpha", 32)

        print(f"Setting up LoRA layers with rank={rank}, alpha={alpha}")
        lora_target_count = 0

        # Add LoRA to more layers for better character learning
        # Include attention layers, feed-forward layers, and projection layers
        for name, module in self.transformer.named_modules():
            # Expand LoRA application to more layer types for better character consistency
            should_add_lora = (
                ("attn" in name or "to_q" in name or "to_k" in name or "to_v" in name or "to_out" in name) or
                ("mlp" in name and "linear" in name) or  # Feed-forward layers
                ("proj" in name)  # Projection layers
                # Removed normalization layers as they can cause issues and aren't typically Linear layers
            ) and isinstance(module, torch.nn.Linear)

            if should_add_lora:
                # Skip if already has LoRA
                if hasattr(module, 'lora_A'):
                    continue

                lora_target_count += 1
                if lora_target_count <= 5:  # Print first few for debugging
                    print(f"  Adding LoRA to: {name} ({type(module).__name__})")

                # Create LoRA layer with same dtype as the module
                lora_layer = LoRALinearLayer(
                    module.in_features,
                    module.out_features,
                    rank=rank,
                    alpha=alpha
                )

                # Convert LoRA layer to same dtype as the original module
                lora_layer = lora_layer.to(dtype=module.weight.dtype)

                # Register as buffer to avoid issues
                self.lora_layers[name] = lora_layer

                # Hook to add LoRA output to original
                def make_hook(lora_layer):
                    def hook(_, input, output):
                        # Ensure input and output have consistent dtype
                        input_tensor = input[0]
                        lora_output = lora_layer(input_tensor)
                        # Ensure lora_output matches output dtype
                        lora_output = lora_output.to(output.dtype)
                        return output + lora_output
                    return hook

                module.register_forward_hook(make_hook(lora_layer))

        # Collect trainable parameters
        self.trainable_params = []
        for lora_layer in self.lora_layers.values():
            self.trainable_params.extend(lora_layer.parameters())

        print(f"Added LoRA to {len(self.lora_layers)} layers")
        print(f"Trainable parameters: {sum(p.numel() for p in self.trainable_params):,}")

    def encode_prompt(self, input_ids, input_ids_2, input_ids_3, attention_mask, attention_mask_2, attention_mask_3):
        """Encode prompts using all three SD3.5 text encoders"""
        # Encode with first text encoder (CLIP L/14)
        text_encoder_output = self.text_encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )
        prompt_embeds_1 = text_encoder_output.last_hidden_state

        # Encode with second text encoder (OpenCLIP bigG/14)
        text_encoder_2_output = self.text_encoder_2(
            input_ids=input_ids_2,
            attention_mask=attention_mask_2,
            return_dict=True
        )
        prompt_embeds_2 = text_encoder_2_output.last_hidden_state
        pooled_prompt_embeds_2 = text_encoder_2_output.text_embeds

        # Encode with third text encoder (T5-XXL)
        text_encoder_3_output = self.text_encoder_3(
            input_ids=input_ids_3,
            attention_mask=attention_mask_3,
            return_dict=True
        )
        prompt_embeds_3 = text_encoder_3_output.last_hidden_state

        # SD3 specific embedding handling
        # Based on the official SD3 implementation, we need to handle embeddings differently

        # Get dimensions
        batch_size = prompt_embeds_1.shape[0]
        clip_seq_len = 77  # CLIP sequence length
        t5_seq_len = 256   # T5 sequence length

        # Concatenate CLIP embeddings along feature dimension
        clip_prompt_embeds = torch.cat([prompt_embeds_1, prompt_embeds_2], dim=-1)

        # Check if we should use CLIP only (fallback for dimension issues)
        if self.config.get("clip_only", False):
            # Use only CLIP embeddings to avoid dimension mismatch issues
            encoder_hidden_states = clip_prompt_embeds
        else:
            # For SD3, we concatenate along sequence dimension, not feature dimension
            # This means we stack CLIP and T5 embeddings as separate sequences

            # Ensure both have the same feature dimension by padding if necessary
            clip_feat_dim = clip_prompt_embeds.shape[-1]
            t5_feat_dim = prompt_embeds_3.shape[-1]

            if clip_feat_dim != t5_feat_dim:
                # Pad the smaller one to match the larger one
                if clip_feat_dim < t5_feat_dim:
                    # Pad CLIP embeddings
                    padding = torch.zeros(
                        batch_size, clip_seq_len, t5_feat_dim - clip_feat_dim,
                        device=clip_prompt_embeds.device, dtype=clip_prompt_embeds.dtype
                    )
                    clip_prompt_embeds = torch.cat([clip_prompt_embeds, padding], dim=-1)
                else:
                    # Pad T5 embeddings
                    padding = torch.zeros(
                        batch_size, t5_seq_len, clip_feat_dim - t5_feat_dim,
                        device=prompt_embeds_3.device, dtype=prompt_embeds_3.dtype
                    )
                    prompt_embeds_3 = torch.cat([prompt_embeds_3, padding], dim=-1)

            # Now concatenate along sequence dimension (dim=1)
            # This creates [batch_size, clip_seq_len + t5_seq_len, feature_dim]
            encoder_hidden_states = torch.cat([clip_prompt_embeds, prompt_embeds_3], dim=1)

        # Fix pooled projections dimension for SD3
        # Based on the error, the transformer's text_embedder.linear_1 expects 2048 input features
        # but we're providing 1280 (from CLIP 2 text_embeds)

        # The correct approach for SD3 is to concatenate pooled embeddings from both CLIP encoders
        # CLIP 1: Use the CLS token (first token) from the last hidden state
        pooled_prompt_embeds_1 = prompt_embeds_1[:, 0, :]  # [batch_size, 768]

        # CLIP 2: Use the text_embeds (already pooled)
        # pooled_prompt_embeds_2 is already [batch_size, 1280]

        # Concatenate both pooled embeddings: 768 + 1280 = 2048
        pooled_projections = torch.cat([pooled_prompt_embeds_1, pooled_prompt_embeds_2], dim=-1)

        # Verify dimension
        expected_dim = 2048
        if pooled_projections.shape[-1] != expected_dim:
            logger.warning(f"Pooled projections dimension mismatch: got {pooled_projections.shape[-1]}, expected {expected_dim}")

            # Adjust dimension if needed
            current_dim = pooled_projections.shape[-1]
            if current_dim < expected_dim:
                # Pad with zeros
                padding_dim = expected_dim - current_dim
                padding = torch.zeros(
                    pooled_projections.shape[0], padding_dim,
                    device=pooled_projections.device, dtype=pooled_projections.dtype
                )
                pooled_projections = torch.cat([pooled_projections, padding], dim=-1)
            elif current_dim > expected_dim:
                # Truncate
                pooled_projections = pooled_projections[:, :expected_dim]

        return encoder_hidden_states, pooled_projections

    def verify_dimensions(self):
        """Verify that our tensor dimensions match what the transformer expects"""
        logger.info("🔍 Verifying tensor dimensions...")

        try:
            # Check what the transformer expects for pooled projections
            if hasattr(self.transformer, 'time_text_embed'):
                time_text_embed = self.transformer.time_text_embed
                if hasattr(time_text_embed, 'text_embedder'):
                    text_embedder = time_text_embed.text_embedder
                    if hasattr(text_embedder, 'linear_1'):
                        expected_pooled_dim = text_embedder.linear_1.in_features
                        logger.info(f"  Expected pooled projection dimension: {expected_pooled_dim}")

                        # Check our text encoder dimensions
                        clip1_dim = self.text_encoder.config.hidden_size
                        clip2_dim = self.text_encoder_2.config.projection_dim
                        our_combined_dim = clip1_dim + clip2_dim

                        logger.info(f"  CLIP 1 dimension: {clip1_dim}")
                        logger.info(f"  CLIP 2 dimension: {clip2_dim}")
                        logger.info(f"  Our combined dimension: {our_combined_dim}")

                        if our_combined_dim == expected_pooled_dim:
                            logger.info("  ✅ Dimensions match!")
                        else:
                            logger.warning(f"  ⚠️ Dimension mismatch! Expected {expected_pooled_dim}, got {our_combined_dim}")

            # Check encoder hidden states dimension
            if hasattr(self.transformer.config, 'joint_attention_dim'):
                expected_encoder_dim = self.transformer.config.joint_attention_dim
                logger.info(f"  Expected encoder hidden states dimension: {expected_encoder_dim}")

        except Exception as e:
            logger.warning(f"Could not verify dimensions: {e}")

        logger.info("Dimension verification complete.")

    def train(self, train_dataloader):
        """Training loop"""
        # Setup optimizer
        optimizer = torch.optim.AdamW(
            self.trainable_params,
            lr=self.config.get("learning_rate", 1e-4),
            betas=(0.9, 0.999),
            weight_decay=self.config.get("weight_decay", 0.01),
            eps=1e-08
        )
        
        # Setup scheduler
        num_training_steps = len(train_dataloader) * self.config.get("num_epochs", 10)
        lr_scheduler = get_scheduler(
            self.config.get("lr_scheduler", "cosine"),
            optimizer=optimizer,
            num_warmup_steps=self.config.get("warmup_steps", 500),
            num_training_steps=num_training_steps
        )
        
        # Prepare for distributed training
        lora_modules = list(self.lora_layers.values())
        optimizer, train_dataloader, lr_scheduler = self.accelerator.prepare(
            optimizer, train_dataloader, lr_scheduler
        )
        
        # Move models to device
        self.transformer.to(self.accelerator.device)
        self.text_encoder.to(self.accelerator.device)
        self.text_encoder_2.to(self.accelerator.device)
        self.text_encoder_3.to(self.accelerator.device)
        self.vae.to(self.accelerator.device)
        
        for lora_layer in lora_modules:
            lora_layer.to(self.accelerator.device)
        
        # Training loop
        global_step = 0
        
        for epoch in range(self.config.get("num_epochs", 10)):
            for batch in tqdm(train_dataloader, desc=f"Epoch {epoch+1}"):
                with self.accelerator.accumulate(self.transformer):
                    # Encode images to latent space with memory optimization
                    pixel_values = batch["pixel_values"].to(self.accelerator.device, dtype=self.vae.dtype)

                    # Use no_grad for VAE encoding to save memory
                    with torch.no_grad():
                        latents = self.vae.encode(pixel_values).latent_dist.sample()
                        latents = latents * self.vae.config.scaling_factor

                    # Ensure latents require grad for training
                    latents = latents.detach().requires_grad_()

                    # Sample noise with same dtype as latents
                    noise = torch.randn_like(latents, dtype=latents.dtype, device=latents.device)
                    bsz = latents.shape[0]

                    # Sample timesteps for flow matching (0 to 1 instead of 0 to 1000)
                    timesteps = torch.rand((bsz,), device=latents.device, dtype=latents.dtype)

                    # Flow matching: interpolate between noise and data
                    # x_t = (1 - t) * noise + t * latents
                    noisy_latents = (1 - timesteps.view(-1, 1, 1, 1)) * noise + timesteps.view(-1, 1, 1, 1) * latents

                    # Encode text with all three encoders
                    input_ids = batch["input_ids"].to(self.accelerator.device)
                    input_ids_2 = batch["input_ids_2"].to(self.accelerator.device)
                    input_ids_3 = batch["input_ids_3"].to(self.accelerator.device)
                    attention_mask = batch["attention_mask"].to(self.accelerator.device)
                    attention_mask_2 = batch["attention_mask_2"].to(self.accelerator.device)
                    attention_mask_3 = batch["attention_mask_3"].to(self.accelerator.device)

                    # Encode text with no_grad to save memory, then detach and require grad
                    with torch.no_grad():
                        encoder_hidden_states, pooled_prompt_embeds = self.encode_prompt(
                            input_ids, input_ids_2, input_ids_3, attention_mask, attention_mask_2, attention_mask_3
                        )

                    # Detach and require grad for training
                    encoder_hidden_states = encoder_hidden_states.detach().requires_grad_()
                    pooled_prompt_embeds = pooled_prompt_embeds.detach().requires_grad_()

                    # Ensure embeddings have correct dtype
                    encoder_hidden_states = encoder_hidden_states.to(dtype=noisy_latents.dtype)
                    pooled_prompt_embeds = pooled_prompt_embeds.to(dtype=noisy_latents.dtype)

                    # Debug: Print shapes on first iteration to verify dimensions
                    if global_step == 0:
                        logger.info(f"Debug - Tensor shapes:")
                        logger.info(f"  noisy_latents: {noisy_latents.shape}")
                        logger.info(f"  timesteps: {timesteps.shape}")
                        logger.info(f"  encoder_hidden_states: {encoder_hidden_states.shape}")
                        logger.info(f"  pooled_prompt_embeds: {pooled_prompt_embeds.shape}")

                    # Predict the velocity (flow direction)
                    try:
                        # Try the standard SD3 transformer call
                        model_pred = self.transformer(
                            hidden_states=noisy_latents,
                            timestep=timesteps,
                            encoder_hidden_states=encoder_hidden_states,
                            pooled_projections=pooled_prompt_embeds,
                            return_dict=False
                        )[0]
                    except RuntimeError as e:
                        if "mat1 and mat2 shapes cannot be multiplied" in str(e):
                            logger.error(f"Matrix multiplication error in transformer:")
                            logger.error(f"  noisy_latents shape: {noisy_latents.shape}")
                            logger.error(f"  encoder_hidden_states shape: {encoder_hidden_states.shape}")
                            logger.error(f"  pooled_prompt_embeds shape: {pooled_prompt_embeds.shape}")
                            logger.error(f"  timesteps shape: {timesteps.shape}")

                            # Try alternative approaches
                            logger.info("Trying fallback approaches...")

                            # Fallback 1: Use only CLIP embeddings (first 77 tokens)
                            try:
                                clip_only_embeds = encoder_hidden_states[:, :77, :]
                                model_pred = self.transformer(
                                    hidden_states=noisy_latents,
                                    timestep=timesteps,
                                    encoder_hidden_states=clip_only_embeds,
                                    pooled_projections=pooled_prompt_embeds,
                                    return_dict=False
                                )[0]
                                logger.info("Fallback 1 (CLIP-only embeddings) successful!")
                            except Exception as fallback1_e:
                                logger.error(f"Fallback 1 failed: {fallback1_e}")

                                # Fallback 2: Try with only CLIP 2 pooled embeddings
                                try:
                                    # Re-encode to get original CLIP 2 pooled embeddings
                                    text_encoder_2_output = self.text_encoder_2(
                                        input_ids=input_ids_2,
                                        attention_mask=attention_mask_2,
                                        return_dict=True
                                    )
                                    original_pooled = text_encoder_2_output.text_embeds

                                    model_pred = self.transformer(
                                        hidden_states=noisy_latents,
                                        timestep=timesteps,
                                        encoder_hidden_states=clip_only_embeds,
                                        pooled_projections=original_pooled,
                                        return_dict=False
                                    )[0]
                                    logger.info("Fallback 2 (original pooled embeddings) successful!")
                                except Exception as fallback2_e:
                                    logger.error(f"Fallback 2 also failed: {fallback2_e}")
                                    logger.error("All fallback approaches failed. This might be a model compatibility issue.")
                                    raise e
                        else:
                            raise e

                    # Flow matching loss: predict v = data - noise
                    target = latents - noise

                    # Compute loss (ensure both tensors have same dtype)
                    model_pred = model_pred.to(dtype=target.dtype)
                    loss = F.mse_loss(model_pred, target, reduction="mean")
                    
                    # Backward pass
                    self.accelerator.backward(loss)

                    if self.accelerator.sync_gradients:
                        # Handle gradient clipping properly for FP16 mixed precision
                        if self.config.get("mixed_precision") == "fp16":
                            # For FP16, we need to handle gradient clipping more carefully
                            # to avoid the "Attempting to unscale FP16 gradients" error
                            try:
                                self.accelerator.clip_grad_norm_(self.trainable_params, 1.0)
                            except ValueError as e:
                                if "Attempting to unscale FP16 gradients" in str(e):
                                    # Skip gradient clipping for this step to avoid the error
                                    # This is a known issue with FP16 and gradient scaling
                                    logger.warning("Skipping gradient clipping due to FP16 unscaling issue")
                                    pass
                                else:
                                    raise e
                        else:
                            # For bf16 and fp32, gradient clipping works normally
                            self.accelerator.clip_grad_norm_(self.trainable_params, 1.0)

                    optimizer.step()
                    lr_scheduler.step()
                    optimizer.zero_grad()

                    # Clear cache to free memory
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                if self.accelerator.sync_gradients:
                    global_step += 1
                    
                    # Log progress
                    if global_step % 100 == 0:
                        logger.info(f"Step {global_step}, Loss: {loss.item():.4f}")
                    
                    # Save checkpoint
                    if global_step % self.config.get("save_steps", 1000) == 0:
                        self.save_checkpoint(global_step)
        
        # Final save
        self.save_checkpoint("final")
    
    # Flow matching doesn't need the old noise scheduling method
    # SD3.5 uses rectified flow which is simpler: x_t = (1-t)*noise + t*data
    
    def save_checkpoint(self, step):
        """Save LoRA weights in Diffusers-compatible format"""
        save_dir = Path(self.config["output_dir"]) / f"checkpoint-{step}"
        save_dir.mkdir(parents=True, exist_ok=True)

        # Save LoRA weights in the format expected by Diffusers
        lora_state_dict = {}
        for name, lora_layer in self.lora_layers.items():
            # Convert our layer names to Diffusers format
            # Our format: "transformer.transformer_blocks.0.attn.to_q"
            # Diffusers format: "transformer.transformer_blocks.0.attn.to_q.lora_A"
            lora_state_dict[f"{name}.lora_A"] = lora_layer.lora_A.data
            lora_state_dict[f"{name}.lora_B"] = lora_layer.lora_B.data

        # Save in both formats for compatibility
        torch.save(lora_state_dict, save_dir / "lora_weights.pt")  # Our format
        torch.save(lora_state_dict, save_dir / "pytorch_lora_weights.bin")  # Diffusers format

        # Also save as safetensors (preferred format)
        try:
            from safetensors.torch import save_file
            save_file(lora_state_dict, save_dir / "pytorch_lora_weights.safetensors")
        except ImportError:
            print("Warning: safetensors not available, skipping .safetensors save")

        # Save config
        with open(save_dir / "config.json", "w") as f:
            json.dump(self.config, f, indent=2)

        logger.info(f"Saved checkpoint to {save_dir}")

def main():
    # Set memory optimization environment variables
    import os
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

    # Clear any existing CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"🔍 GPU Memory: {total_memory:.1f}GB total")
        if total_memory < 16:
            print("⚠️  Low VRAM detected! Consider using --low_vram flag")

    parser = argparse.ArgumentParser(description="Train multi-character LoRA")
    parser.add_argument("--data_root", type=str, required=True, help="Root directory containing character folders")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for checkpoints")
    parser.add_argument("--model_id", type=str, default="stabilityai/stable-diffusion-3.5-large")
    parser.add_argument("--resolution", type=int, default=1024)
    parser.add_argument("--batch_size", type=int, default=1)  # Reduced default batch size for memory
    parser.add_argument("--num_epochs", type=int, default=10)
    parser.add_argument("--learning_rate", type=float, default=1e-4)
    parser.add_argument("--lora_rank", type=int, default=8)  # Increased for better character learning
    parser.add_argument("--lora_alpha", type=float, default=64)  # Increased for stronger adaptation
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--mixed_precision", type=str, default="auto", choices=["no", "fp16", "bf16", "auto"],
                        help="Mixed precision mode. 'auto' will use 'no' for MPS, 'fp16' for CUDA. Use 'no' if you encounter dtype errors")
    parser.add_argument("--force_fp32", action="store_true",
                        help="Force all operations to use fp32 (disables mixed precision completely)")
    parser.add_argument("--clip_only", action="store_true",
                        help="Use only CLIP embeddings (skip T5) to avoid dimension issues")
    parser.add_argument("--low_vram", action="store_true",
                        help="Enable aggressive memory optimizations for low VRAM systems")
    parser.add_argument("--cpu_offload", action="store_true",
                        help="Offload models to CPU when not in use (slower but saves VRAM)")
    
    args = parser.parse_args()
    
    # Create config
    config = {
        "data_root": args.data_root,
        "output_dir": args.output_dir,
        "model_id": args.model_id,
        "resolution": args.resolution,
        "batch_size": args.batch_size,
        "num_epochs": args.num_epochs,
        "learning_rate": args.learning_rate,
        "lora_rank": args.lora_rank,
        "lora_alpha": args.lora_alpha,
        "seed": args.seed,
        "gradient_accumulation_steps": 4,  # Increased to compensate for smaller batch size
        "save_steps": 500,
        "warmup_steps": 100,
        "mixed_precision": (
            "no" if args.force_fp32 or (args.mixed_precision == "auto" and torch.backends.mps.is_available())
            else "fp16" if args.mixed_precision == "auto" and torch.cuda.is_available()
            else "no" if args.mixed_precision == "auto"
            else args.mixed_precision
        ),
        "clip_only": args.clip_only,
        "low_vram": args.low_vram,
        "cpu_offload": args.cpu_offload
    }
    
    # Setup dataset
    tokenizer = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer")
    tokenizer_2 = CLIPTokenizer.from_pretrained(args.model_id, subfolder="tokenizer_2")
    tokenizer_3 = T5TokenizerFast.from_pretrained(args.model_id, subfolder="tokenizer_3")

    dataset = MultiCharacterDataset(
        args.data_root,
        tokenizer,
        tokenizer_2,
        tokenizer_3,
        size=args.resolution
    )

    # Check if dataset has any samples
    if len(dataset) == 0:
        print("ERROR: Dataset is empty! Please check your data directory structure.")
        print(f"Expected structure: {args.data_root}/character_name/image.ext and {args.data_root}/character_name/image.txt")
        return

    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    # Create trainer and start training
    trainer = LoRATrainer(config)
    trainer.train(dataloader)
    
    print("Training completed!")

if __name__ == "__main__":
    main()
